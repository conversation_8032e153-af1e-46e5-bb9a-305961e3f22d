{"name": "Tester", "lighting": "none", "layouts": {"width": 22.5, "height": 6.25, "optionKeys": {}, "keys": [{"row": 0, "col": 0, "x": 0, "y": 0, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "accent"}, {"row": 0, "col": 1, "x": 2, "y": 0, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 2, "x": 3, "y": 0, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 3, "x": 4, "y": 0, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 4, "x": 5, "y": 0, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 5, "x": 6.5, "y": 0, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 6, "x": 7.5, "y": 0, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 7, "x": 8.5, "y": 0, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 8, "x": 9.5, "y": 0, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 9, "x": 11, "y": 0, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 10, "x": 12, "y": 0, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 11, "x": 13, "y": 0, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 12, "x": 14, "y": 0, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 13, "x": 15.25, "y": 0, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 14, "x": 16.25, "y": 0, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 15, "x": 17.25, "y": 0, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 16, "x": 18.5, "y": 0, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 17, "x": 19.5, "y": 0, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 18, "x": 20.5, "y": 0, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 19, "x": 21.5, "y": 0, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 20, "x": 0, "y": 1.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 21, "x": 1, "y": 1.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 22, "x": 2, "y": 1.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 23, "x": 3, "y": 1.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 24, "x": 4, "y": 1.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 25, "x": 5, "y": 1.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 26, "x": 6, "y": 1.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 27, "x": 7, "y": 1.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 28, "x": 8, "y": 1.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 29, "x": 9, "y": 1.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 30, "x": 10, "y": 1.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 31, "x": 11, "y": 1.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 32, "x": 12, "y": 1.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 33, "x": 13, "y": 1.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 2, "color": "mod"}, {"row": 0, "col": 34, "x": 15.25, "y": 1.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 35, "x": 16.25, "y": 1.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 36, "x": 17.25, "y": 1.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 37, "x": 18.5, "y": 1.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 38, "x": 19.5, "y": 1.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 39, "x": 20.5, "y": 1.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 40, "x": 21.5, "y": 1.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 41, "x": 0, "y": 2.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1.5, "color": "mod"}, {"row": 0, "col": 42, "x": 1.5, "y": 2.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 43, "x": 2.5, "y": 2.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 44, "x": 3.5, "y": 2.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 45, "x": 4.5, "y": 2.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 46, "x": 5.5, "y": 2.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 47, "x": 6.5, "y": 2.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 48, "x": 7.5, "y": 2.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 49, "x": 8.5, "y": 2.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 50, "x": 9.5, "y": 2.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 51, "x": 10.5, "y": 2.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 52, "x": 11.5, "y": 2.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 53, "x": 12.5, "y": 2.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 54, "x": 13.5, "y": 2.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1.5, "color": "alpha"}, {"row": 0, "col": 55, "x": 15.25, "y": 2.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 56, "x": 16.25, "y": 2.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 57, "x": 17.25, "y": 2.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 58, "x": 18.5, "y": 2.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 59, "x": 19.5, "y": 2.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 60, "x": 20.5, "y": 2.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 61, "x": 21.5, "y": 2.25, "r": 0, "rx": 0, "ry": 0, "h": 2, "w": 1, "color": "mod"}, {"row": 0, "col": 62, "x": 0, "y": 3.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1.75, "color": "mod"}, {"row": 0, "col": 63, "x": 1.75, "y": 3.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 64, "x": 2.75, "y": 3.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 65, "x": 3.75, "y": 3.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 66, "x": 4.75, "y": 3.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 67, "x": 5.75, "y": 3.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 68, "x": 6.75, "y": 3.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 69, "x": 7.75, "y": 3.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 70, "x": 8.75, "y": 3.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 71, "x": 9.75, "y": 3.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 72, "x": 10.75, "y": 3.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 73, "x": 11.75, "y": 3.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 74, "x": 12.75, "y": 3.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 2.25, "color": "accent"}, {"row": 0, "col": 75, "x": 18.5, "y": 3.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 76, "x": 19.5, "y": 3.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 77, "x": 20.5, "y": 3.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 78, "x": 0, "y": 4.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 2.25, "color": "mod"}, {"row": 0, "col": 79, "x": 2.25, "y": 4.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 80, "x": 3.25, "y": 4.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 81, "x": 4.25, "y": 4.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 82, "x": 5.25, "y": 4.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 83, "x": 6.25, "y": 4.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 84, "x": 7.25, "y": 4.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 85, "x": 8.25, "y": 4.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 86, "x": 9.25, "y": 4.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 87, "x": 10.25, "y": 4.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 88, "x": 11.25, "y": 4.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 89, "x": 12.25, "y": 4.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 2.75, "color": "mod"}, {"row": 0, "col": 90, "x": 16.25, "y": 4.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 91, "x": 18.5, "y": 4.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 92, "x": 19.5, "y": 4.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 93, "x": 20.5, "y": 4.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}, {"row": 0, "col": 94, "x": 21.5, "y": 4.25, "r": 0, "rx": 0, "ry": 0, "h": 2, "w": 1, "color": "accent"}, {"row": 0, "col": 95, "x": 0, "y": 5.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1.25, "color": "mod"}, {"row": 0, "col": 96, "x": 1.25, "y": 5.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1.25, "color": "mod"}, {"row": 0, "col": 97, "x": 2.5, "y": 5.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1.25, "color": "mod"}, {"row": 0, "col": 98, "x": 3.75, "y": 5.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 6.25, "color": "alpha"}, {"row": 0, "col": 99, "x": 10, "y": 5.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1.25, "color": "mod"}, {"row": 0, "col": 100, "x": 11.25, "y": 5.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1.25, "color": "mod"}, {"row": 0, "col": 101, "x": 12.5, "y": 5.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1.25, "color": "mod"}, {"row": 0, "col": 102, "x": 13.75, "y": 5.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1.25, "color": "mod"}, {"row": 0, "col": 103, "x": 15.25, "y": 5.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 104, "x": 16.25, "y": 5.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 105, "x": 17.25, "y": 5.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "mod"}, {"row": 0, "col": 106, "x": 18.5, "y": 5.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 2, "color": "alpha"}, {"row": 0, "col": 107, "x": 20.5, "y": 5.25, "r": 0, "rx": 0, "ry": 0, "h": 1, "w": 1, "color": "alpha"}]}, "matrix": {"rows": 1, "cols": 108}, "vendorProductId": 0}