import React, { useState, useEffect, useCallback } from 'react';
import { Card, Button, List, Typography, Space, Modal, message, Progress, Tooltip } from 'antd';
import { PlayCircleOutlined, StopOutlined, DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import MacroRecorder from './MacroRecorder';
import MacroEditor from './MacroEditor';
import { MacroAPI } from '../../utils/macroApi';
import './MacroRecording.css';

const { Title, Text } = Typography;

const MacroRecording = () => {
  const { t } = useTranslation();
  const { device } = useHandleDevice();

  // State for macro management
  const [macros, setMacros] = useState(Array(16).fill(null).map((_, index) => ({
    id: index,
    name: `M${index}`,
    sequence: [],
    isEmpty: true,
    size: 0
  })));

  const [selectedMacro, setSelectedMacro] = useState(0);
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showRecorder, setShowRecorder] = useState(false);
  const [showEditor, setShowEditor] = useState(false);
  const [macroBufferSize, setMacroBufferSize] = useState(1024);
  const [usedBufferSize, setUsedBufferSize] = useState(0);
  const [macroApi, setMacroApi] = useState(null);

  // Initialize macro API and load macros from device
  useEffect(() => {
    if (device) {
      const api = new MacroAPI(device);
      setMacroApi(api);
      loadMacrosFromDevice(api);
    }
  }, [device]);

  const loadMacrosFromDevice = async (api = macroApi) => {
    if (!api) return;

    try {
      // Get buffer size and macro count
      const bufferSize = await api.getMacroBufferSize();
      const macroCount = await api.getMacroCount();

      setMacroBufferSize(bufferSize);

      // Load all macros
      const macroSequences = await api.readAllMacros();

      // Update state with loaded macros
      const loadedMacros = macroSequences.map((sequence, index) => ({
        id: index,
        name: `M${index}`,
        sequence: sequence,
        isEmpty: sequence.length === 0,
        size: calculateSequenceSize(sequence)
      }));

      setMacros(loadedMacros);

      // Calculate used buffer size
      const totalSize = loadedMacros.reduce((sum, macro) => sum + macro.size, 0);
      setUsedBufferSize(totalSize);

    } catch (error) {
      console.error('Failed to load macros:', error);
      message.error(t('macro.load_error'));
    }
  };

  const calculateSequenceSize = (sequence) => {
    // Calculate approximate size in bytes
    let size = 0;
    for (const item of sequence) {
      if (item.type === 'key') {
        size += 2; // Action byte + keycode byte
      } else if (item.type === 'delay') {
        size += 2 + item.delay.toString().length; // Action byte + delay string + terminator
      } else if (item.type === 'character') {
        size += 1; // Character byte
      }
    }
    size += 1; // Macro terminator
    return size;
  };

  const saveMacroToDevice = async (macroIndex, sequence) => {
    if (!macroApi) {
      message.error('Device not connected');
      return;
    }

    try {
      // Update the macro in our local array
      const updatedMacros = [...macros];
      updatedMacros[macroIndex] = {
        ...updatedMacros[macroIndex],
        sequence,
        isEmpty: sequence.length === 0,
        size: calculateSequenceSize(sequence)
      };

      // Write all macros to device
      const macroSequences = updatedMacros.map(macro => macro.sequence);
      await macroApi.writeAllMacros(macroSequences);

      // Update local state
      setMacros(updatedMacros);

      // Update buffer usage
      const totalSize = updatedMacros.reduce((sum, macro) => sum + macro.size, 0);
      setUsedBufferSize(totalSize);

      message.success(t('macro.save_success'));
    } catch (error) {
      console.error('Failed to save macro:', error);
      message.error(t('macro.save_error'));
    }
  };

  const deleteMacro = (macroIndex) => {
    Modal.confirm({
      title: t('macro.delete_confirm'),
      onOk: () => {
        saveMacroToDevice(macroIndex, []);
      }
    });
  };

  const playMacro = async (macroIndex) => {
    if (macros[macroIndex].isEmpty) {
      message.warning(t('macro.empty_macro_warning'));
      return;
    }

    setIsPlaying(true);
    try {
      // TODO: Implement macro playback
      console.log('Playing macro:', macroIndex);
      message.success(t('macro.play_success'));
    } catch (error) {
      console.error('Failed to play macro:', error);
      message.error(t('macro.play_error'));
    } finally {
      setIsPlaying(false);
    }
  };

  const handleRecordComplete = (sequence) => {
    saveMacroToDevice(selectedMacro, sequence);
    setShowRecorder(false);
  };

  const handleEditComplete = (sequence) => {
    saveMacroToDevice(selectedMacro, sequence);
    setShowEditor(false);
  };

  const calculateBufferUsage = () => {
    const used = macros.reduce((total, macro) => total + macro.size, 0);
    setUsedBufferSize(used);
    return (used / macroBufferSize) * 100;
  };

  const renderMacroItem = (macro) => (
    <List.Item
      key={macro.id}
      className={`macro-item ${selectedMacro === macro.id ? 'selected' : ''}`}
      onClick={() => setSelectedMacro(macro.id)}
      actions={[
        <Tooltip title={t('macro.play')}>
          <Button
            type="text"
            icon={<PlayCircleOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              playMacro(macro.id);
            }}
            disabled={macro.isEmpty || isPlaying}
          />
        </Tooltip>,
        <Tooltip title={t('macro.edit_macro')}>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              setSelectedMacro(macro.id);
              setShowEditor(true);
            }}
            disabled={macro.isEmpty}
          />
        </Tooltip>,
        <Tooltip title={t('macro.delete')}>
          <Button
            type="text"
            icon={<DeleteOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              deleteMacro(macro.id);
            }}
            disabled={macro.isEmpty}
            danger
          />
        </Tooltip>
      ]}
    >
      <List.Item.Meta
        title={macro.name}
        description={
          macro.isEmpty
            ? t('macro.empty_macro')
            : `${t('macro.macro_length')}: ${macro.sequence.length}, ${t('macro.macro_size')}: ${macro.size} bytes`
        }
      />
    </List.Item>
  );

  return (
    <div className="macro-recording-container">
      <div className="macro-header">
        <Title level={2}>{t('macro.title')}</Title>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setShowRecorder(true)}
            disabled={isRecording || isPlaying}
          >
            {t('macro.record_new')}
          </Button>
        </Space>
      </div>

      <div className="macro-content">
        <div className="macro-list-section">
          <Card title={`${t('macro.macro_count')}: ${macros.filter(m => !m.isEmpty).length}/16`}>
            <List
              dataSource={macros}
              renderItem={renderMacroItem}
              className="macro-list"
            />
          </Card>
        </div>

        <div className="macro-details-section">
          <Card title={t('macro.buffer_usage')}>
            <Progress
              percent={calculateBufferUsage()}
              format={(percent) => `${usedBufferSize}/${macroBufferSize} bytes (${percent?.toFixed(1)}%)`}
              status={calculateBufferUsage() > 90 ? 'exception' : 'normal'}
            />
            <div className="buffer-info">
              <Text type="secondary">
                {t('macro.buffer_usage_tip')}
              </Text>
            </div>
          </Card>

          {selectedMacro !== null && !macros[selectedMacro].isEmpty && (
            <Card title={`${macros[selectedMacro].name} ${t('macro.sequence')}`}>
              <div className="macro-sequence-preview">
                {macros[selectedMacro].sequence.map((item, index) => (
                  <span key={index} className="sequence-item">
                    {item.type === 'key' ? item.key : `${item.delay}ms`}
                  </span>
                ))}
              </div>
            </Card>
          )}
        </div>
      </div>

      <MacroRecorder
        visible={showRecorder}
        onComplete={handleRecordComplete}
        onCancel={() => setShowRecorder(false)}
        macroName={macros[selectedMacro]?.name}
      />

      <MacroEditor
        visible={showEditor}
        sequence={macros[selectedMacro]?.sequence || []}
        onComplete={handleEditComplete}
        onCancel={() => setShowEditor(false)}
        macroName={macros[selectedMacro]?.name}
      />
    </div>
  );
};

export default MacroRecording;
