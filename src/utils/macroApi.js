import { changeToHex, changeToHighLowHex } from './hidUtils';

// VIA Protocol Commands for Macros (0x0C-0x10)
export const VIA_COMMANDS = {
  DYNAMIC_KEYMAP_MACRO_GET_COUNT: 0x0C,
  DYNAMIC_KEYMAP_MACRO_GET_BUFFER_SIZE: 0x0D,
  DYNAMIC_KEYMAP_MACRO_GET_BUFFER: 0x0E,
  DYNAMIC_KEYMAP_MACRO_SET_BUFFER: 0x0F,
  DYNAMIC_KEYMAP_MACRO_RESET: 0x10
};

// Macro action types (based on QMK sendstring magic codes)
export const MACRO_ACTIONS = {
  TAP: 0x01,
  DOWN: 0x02,
  UP: 0x03,
  DELAY: 0x04
};

// Macro terminator
export const MACRO_TERMINATOR = 0x00;

/**
 * Macro API class for handling VIA protocol macro operations
 */
export class MacroAPI {
  constructor(device) {
    this.device = device;
    this.macroCount = 16; // Default to 16 macros (M0-M15)
    this.bufferSize = 1024; // Default buffer size
  }

  /**
   * Send HID command to device
   * @param {number} command - VIA command code
   * @param {Array} data - Command data bytes
   * @returns {Promise<Uint8Array>} Response from device
   */
  async sendCommand(command, data = []) {
    if (!this.device) {
      throw new Error('No device connected');
    }

    try {
      // Prepare command packet (32 bytes for HID)
      const packet = new Uint8Array(32);
      packet[0] = command;
      
      // Copy data to packet
      for (let i = 0; i < data.length && i < 31; i++) {
        packet[i + 1] = data[i];
      }

      // Send command
      await this.device.sendReport(0, packet);
      
      // Read response
      const response = await this.device.receiveReport();
      return response;
    } catch (error) {
      console.error('Failed to send macro command:', error);
      throw error;
    }
  }

  /**
   * Get the number of available macro slots
   * @returns {Promise<number>} Number of macro slots
   */
  async getMacroCount() {
    try {
      const response = await this.sendCommand(VIA_COMMANDS.DYNAMIC_KEYMAP_MACRO_GET_COUNT);
      this.macroCount = response[1];
      return this.macroCount;
    } catch (error) {
      console.error('Failed to get macro count:', error);
      return this.macroCount; // Return default
    }
  }

  /**
   * Get the macro buffer size
   * @returns {Promise<number>} Buffer size in bytes
   */
  async getMacroBufferSize() {
    try {
      const response = await this.sendCommand(VIA_COMMANDS.DYNAMIC_KEYMAP_MACRO_GET_BUFFER_SIZE);
      // Buffer size is 16-bit (high byte, low byte)
      this.bufferSize = (response[1] << 8) | response[2];
      return this.bufferSize;
    } catch (error) {
      console.error('Failed to get macro buffer size:', error);
      return this.bufferSize; // Return default
    }
  }

  /**
   * Read macro buffer from device
   * @param {number} offset - Buffer offset
   * @param {number} size - Number of bytes to read (max 28)
   * @returns {Promise<Uint8Array>} Buffer data
   */
  async getMacroBuffer(offset = 0, size = 28) {
    try {
      const offsetHigh = (offset >> 8) & 0xFF;
      const offsetLow = offset & 0xFF;
      const data = [offsetHigh, offsetLow, size];
      
      const response = await this.sendCommand(VIA_COMMANDS.DYNAMIC_KEYMAP_MACRO_GET_BUFFER, data);
      
      // Extract data portion (skip command echo)
      return response.slice(4, 4 + size);
    } catch (error) {
      console.error('Failed to read macro buffer:', error);
      throw error;
    }
  }

  /**
   * Write macro buffer to device
   * @param {number} offset - Buffer offset
   * @param {Uint8Array} data - Data to write (max 28 bytes)
   * @returns {Promise<void>}
   */
  async setMacroBuffer(offset, data) {
    try {
      const offsetHigh = (offset >> 8) & 0xFF;
      const offsetLow = offset & 0xFF;
      const size = Math.min(data.length, 28);
      
      const commandData = [offsetHigh, offsetLow, size, ...Array.from(data.slice(0, size))];
      
      await this.sendCommand(VIA_COMMANDS.DYNAMIC_KEYMAP_MACRO_SET_BUFFER, commandData);
    } catch (error) {
      console.error('Failed to write macro buffer:', error);
      throw error;
    }
  }

  /**
   * Reset all macros (clear buffer)
   * @returns {Promise<void>}
   */
  async resetMacros() {
    try {
      await this.sendCommand(VIA_COMMANDS.DYNAMIC_KEYMAP_MACRO_RESET);
    } catch (error) {
      console.error('Failed to reset macros:', error);
      throw error;
    }
  }

  /**
   * Read all macros from device
   * @returns {Promise<Array>} Array of macro sequences
   */
  async readAllMacros() {
    try {
      const bufferSize = await this.getMacroBufferSize();
      const macroCount = await this.getMacroCount();
      
      // Read entire buffer in chunks
      const buffer = new Uint8Array(bufferSize);
      const chunkSize = 28;
      
      for (let offset = 0; offset < bufferSize; offset += chunkSize) {
        const size = Math.min(chunkSize, bufferSize - offset);
        const chunk = await this.getMacroBuffer(offset, size);
        buffer.set(chunk, offset);
      }

      // Parse macros from buffer
      return this.parseMacroBuffer(buffer, macroCount);
    } catch (error) {
      console.error('Failed to read all macros:', error);
      throw error;
    }
  }

  /**
   * Write all macros to device
   * @param {Array} macros - Array of macro sequences
   * @returns {Promise<void>}
   */
  async writeAllMacros(macros) {
    try {
      // Convert macros to buffer
      const buffer = this.macrosToBuffer(macros);
      
      // Clear existing macros first
      await this.resetMacros();
      
      // Write buffer in chunks
      const chunkSize = 28;
      for (let offset = 0; offset < buffer.length; offset += chunkSize) {
        const chunk = buffer.slice(offset, offset + chunkSize);
        await this.setMacroBuffer(offset, chunk);
      }
    } catch (error) {
      console.error('Failed to write all macros:', error);
      throw error;
    }
  }

  /**
   * Parse macro buffer into individual macro sequences
   * @param {Uint8Array} buffer - Raw macro buffer
   * @param {number} macroCount - Number of macros to parse
   * @returns {Array} Array of parsed macro sequences
   */
  parseMacroBuffer(buffer, macroCount) {
    const macros = [];
    let bufferIndex = 0;
    let macroIndex = 0;

    while (bufferIndex < buffer.length && macroIndex < macroCount) {
      const sequence = [];
      
      // Parse macro sequence until terminator
      while (bufferIndex < buffer.length) {
        const byte = buffer[bufferIndex++];
        
        if (byte === MACRO_TERMINATOR) {
          break; // End of macro
        }
        
        switch (byte) {
          case MACRO_ACTIONS.TAP:
          case MACRO_ACTIONS.DOWN:
          case MACRO_ACTIONS.UP:
            if (bufferIndex < buffer.length) {
              const keyCode = buffer[bufferIndex++];
              sequence.push({
                type: 'key',
                action: byte === MACRO_ACTIONS.TAP ? 'tap' : 
                       byte === MACRO_ACTIONS.DOWN ? 'down' : 'up',
                key: this.keycodeToString(keyCode)
              });
            }
            break;
            
          case MACRO_ACTIONS.DELAY:
            // Read delay value (variable length)
            let delayStr = '';
            while (bufferIndex < buffer.length) {
              const delayByte = buffer[bufferIndex++];
              if (delayByte === MACRO_TERMINATOR) {
                bufferIndex--; // Back up to process terminator
                break;
              }
              delayStr += String.fromCharCode(delayByte);
            }
            const delay = parseInt(delayStr) || 0;
            sequence.push({
              type: 'delay',
              delay: delay
            });
            break;
            
          default:
            // Character stream or unknown byte
            sequence.push({
              type: 'character',
              char: String.fromCharCode(byte)
            });
            break;
        }
      }
      
      macros.push(sequence);
      macroIndex++;
    }
    
    // Fill remaining slots with empty macros
    while (macros.length < macroCount) {
      macros.push([]);
    }
    
    return macros;
  }

  /**
   * Convert macro sequences to buffer format
   * @param {Array} macros - Array of macro sequences
   * @returns {Uint8Array} Macro buffer
   */
  macrosToBuffer(macros) {
    const bufferData = [];
    
    for (const macro of macros) {
      for (const item of macro) {
        switch (item.type) {
          case 'key':
            const actionCode = item.action === 'tap' ? MACRO_ACTIONS.TAP :
                             item.action === 'down' ? MACRO_ACTIONS.DOWN :
                             MACRO_ACTIONS.UP;
            bufferData.push(actionCode);
            bufferData.push(this.stringToKeycode(item.key));
            break;
            
          case 'delay':
            bufferData.push(MACRO_ACTIONS.DELAY);
            const delayStr = item.delay.toString();
            for (const char of delayStr) {
              bufferData.push(char.charCodeAt(0));
            }
            bufferData.push(MACRO_TERMINATOR);
            break;
            
          case 'character':
            bufferData.push(item.char.charCodeAt(0));
            break;
        }
      }
      
      // Add macro terminator
      bufferData.push(MACRO_TERMINATOR);
    }
    
    return new Uint8Array(bufferData);
  }

  /**
   * Convert keycode to string representation
   * @param {number} keycode - HID keycode
   * @returns {string} Key string
   */
  keycodeToString(keycode) {
    // Basic keycode to string mapping
    const keycodeMap = {
      0x04: 'KeyA', 0x05: 'KeyB', 0x06: 'KeyC', 0x07: 'KeyD', 0x08: 'KeyE',
      0x09: 'KeyF', 0x0A: 'KeyG', 0x0B: 'KeyH', 0x0C: 'KeyI', 0x0D: 'KeyJ',
      0x0E: 'KeyK', 0x0F: 'KeyL', 0x10: 'KeyM', 0x11: 'KeyN', 0x12: 'KeyO',
      0x13: 'KeyP', 0x14: 'KeyQ', 0x15: 'KeyR', 0x16: 'KeyS', 0x17: 'KeyT',
      0x18: 'KeyU', 0x19: 'KeyV', 0x1A: 'KeyW', 0x1B: 'KeyX', 0x1C: 'KeyY',
      0x1D: 'KeyZ',
      0x1E: 'Digit1', 0x1F: 'Digit2', 0x20: 'Digit3', 0x21: 'Digit4', 0x22: 'Digit5',
      0x23: 'Digit6', 0x24: 'Digit7', 0x25: 'Digit8', 0x26: 'Digit9', 0x27: 'Digit0',
      0x28: 'Enter', 0x29: 'Escape', 0x2A: 'Backspace', 0x2B: 'Tab', 0x2C: 'Space'
    };
    
    return keycodeMap[keycode] || `Unknown_${keycode}`;
  }

  /**
   * Convert string to keycode
   * @param {string} keyString - Key string
   * @returns {number} HID keycode
   */
  stringToKeycode(keyString) {
    // Basic string to keycode mapping
    const stringMap = {
      'KeyA': 0x04, 'KeyB': 0x05, 'KeyC': 0x06, 'KeyD': 0x07, 'KeyE': 0x08,
      'KeyF': 0x09, 'KeyG': 0x0A, 'KeyH': 0x0B, 'KeyI': 0x0C, 'KeyJ': 0x0D,
      'KeyK': 0x0E, 'KeyL': 0x0F, 'KeyM': 0x10, 'KeyN': 0x11, 'KeyO': 0x12,
      'KeyP': 0x13, 'KeyQ': 0x14, 'KeyR': 0x15, 'KeyS': 0x16, 'KeyT': 0x17,
      'KeyU': 0x18, 'KeyV': 0x19, 'KeyW': 0x1A, 'KeyX': 0x1B, 'KeyY': 0x1C,
      'KeyZ': 0x1D,
      'Digit1': 0x1E, 'Digit2': 0x1F, 'Digit3': 0x20, 'Digit4': 0x21, 'Digit5': 0x22,
      'Digit6': 0x23, 'Digit7': 0x24, 'Digit8': 0x25, 'Digit9': 0x26, 'Digit0': 0x27,
      'Enter': 0x28, 'Escape': 0x29, 'Backspace': 0x2A, 'Tab': 0x2B, 'Space': 0x2C
    };
    
    return stringMap[keyString] || 0x00;
  }
}

export default MacroAPI;
