.macro-recording-container {
  padding: 20px;
  height: 100%;
  background: #0B0C0E;
  color: white;
}

.macro-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #2a2a2a;
}

.macro-header h2 {
  color: white;
  margin: 0;
}

.macro-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  height: calc(100vh - 200px);
}

.macro-list-section {
  overflow: hidden;
}

.macro-list-section .ant-card {
  height: 100%;
  background: #1a1a1a;
  border: 1px solid #2a2a2a;
}

.macro-list-section .ant-card-head {
  background: #1a1a1a;
  border-bottom: 1px solid #2a2a2a;
}

.macro-list-section .ant-card-head-title {
  color: white;
}

.macro-list-section .ant-card-body {
  padding: 0;
  height: calc(100% - 57px);
  overflow-y: auto;
}

.macro-list {
  height: 100%;
}

.macro-item {
  padding: 16px 24px;
  border-bottom: 1px solid #2a2a2a;
  cursor: pointer;
  transition: background-color 0.2s;
  background: #1a1a1a;
}

.macro-item:hover {
  background: #2a2a2a;
}

.macro-item.selected {
  background: #3a3a3a;
  border-left: 3px solid #1890ff;
}

.macro-item .ant-list-item-meta-title {
  color: white;
  font-weight: 600;
}

.macro-item .ant-list-item-meta-description {
  color: #888;
}

.macro-item .ant-list-item-action {
  margin-left: 16px;
}

.macro-item .ant-btn {
  color: #888;
  border: none;
  background: transparent;
}

.macro-item .ant-btn:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.macro-item .ant-btn.ant-btn-dangerous:hover {
  color: #ff4d4f;
}

.macro-details-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.macro-details-section .ant-card {
  background: #1a1a1a;
  border: 1px solid #2a2a2a;
}

.macro-details-section .ant-card-head {
  background: #1a1a1a;
  border-bottom: 1px solid #2a2a2a;
}

.macro-details-section .ant-card-head-title {
  color: white;
}

.macro-details-section .ant-card-body {
  background: #1a1a1a;
}

.buffer-info {
  margin-top: 12px;
}

.macro-sequence-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.sequence-item {
  display: inline-block;
  padding: 4px 8px;
  background: #2a2a2a;
  border: 1px solid #3a3a3a;
  border-radius: 4px;
  font-size: 12px;
  color: white;
}

.sequence-item:nth-child(even) {
  background: #3a3a3a;
}

/* Progress bar styling */
.ant-progress-text {
  color: white !important;
}

.ant-progress-bg {
  background: #2a2a2a !important;
}

/* Button styling */
.ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* Modal styling */
.ant-modal-content {
  background: #1a1a1a;
}

.ant-modal-header {
  background: #1a1a1a;
  border-bottom: 1px solid #2a2a2a;
}

.ant-modal-title {
  color: white;
}

.ant-modal-body {
  background: #1a1a1a;
  color: white;
}

.ant-modal-footer {
  background: #1a1a1a;
  border-top: 1px solid #2a2a2a;
}

/* List styling */
.ant-list-item {
  border-bottom-color: #2a2a2a !important;
}

/* Tooltip styling */
.ant-tooltip-inner {
  background: #2a2a2a;
  color: white;
}

.ant-tooltip-arrow::before {
  background: #2a2a2a;
}

/* Responsive design */
@media (max-width: 1200px) {
  .macro-content {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .macro-list-section {
    max-height: 400px;
  }
}

@media (max-width: 768px) {
  .macro-recording-container {
    padding: 12px;
  }
  
  .macro-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .macro-content {
    height: auto;
  }
}
