{
  "include": ["src", "types"],
  "compilerOptions": {
    "target": "ESNext",
    "useDefineForClassFields": true,
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "allowJs": false,
    "skipLibCheck": true,
    "esModuleInterop": false,
    "strict": true,
    "module": "ESNext",
    "moduleResolution": "Node",
    "isolatedModules": true,
    "noEmit": true,
    "baseUrl": "./",
    "jsx": "react-jsx",
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true,
  },
  "references": [{ "path": "./tsconfig.node.json" }]
}
