{"routes": [{"route": "/api/*", "methods": ["POST"], "allowedRoles": ["anonymous"]}, {"route": "/css/*", "headers": {"cache-control": "public, max-age=18144000"}}, {"route": "/models/*", "headers": {"cache-control": "public, max-age=18144000"}}, {"route": "/fonts/*", "headers": {"cache-control": "public, max-age=18144000"}}, {"route": "/js/*", "headers": {"cache-control": "public, max-age=18144000"}}, {"route": "/errors", "rewrite": "/"}, {"route": "/test", "rewrite": "/"}, {"route": "/design", "rewrite": "/"}, {"route": "/settings", "rewrite": "/"}, {"route": "/debug", "rewrite": "/"}], "platform": {"apiRuntime": "node:18"}}