import React, { useState, useEffect } from 'react';
import { Modal, Button, List, Input, Select, Space, Typography, InputNumber, Popconfirm } from 'antd';
import { PlusOutlined, DeleteOutlined, ArrowUpOutlined, ArrowDownOutlined, SaveOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;
const { Option } = Select;

const MacroEditor = ({ visible, sequence, onComplete, onCancel, macroName }) => {
  const { t } = useTranslation();
  const [editedSequence, setEditedSequence] = useState([]);
  const [newItemType, setNewItemType] = useState('key');
  const [newKeyCode, setNewKeyCode] = useState('KeyA');
  const [newKeyAction, setNewKeyAction] = useState('tap');
  const [newDelay, setNewDelay] = useState(100);

  useEffect(() => {
    if (visible) {
      setEditedSequence([...sequence]);
    }
  }, [visible, sequence]);

  const addItem = () => {
    const newItem = newItemType === 'delay' 
      ? { type: 'delay', delay: newDelay }
      : { type: 'key', key: newKeyCode, action: newKeyAction };
    
    setEditedSequence([...editedSequence, newItem]);
  };

  const removeItem = (index) => {
    const newSequence = editedSequence.filter((_, i) => i !== index);
    setEditedSequence(newSequence);
  };

  const moveItem = (index, direction) => {
    const newSequence = [...editedSequence];
    const targetIndex = direction === 'up' ? index - 1 : index + 1;
    
    if (targetIndex >= 0 && targetIndex < newSequence.length) {
      [newSequence[index], newSequence[targetIndex]] = [newSequence[targetIndex], newSequence[index]];
      setEditedSequence(newSequence);
    }
  };

  const updateItem = (index, field, value) => {
    const newSequence = [...editedSequence];
    newSequence[index] = { ...newSequence[index], [field]: value };
    setEditedSequence(newSequence);
  };

  const saveSequence = () => {
    onComplete(editedSequence);
  };

  const renderSequenceItem = (item, index) => {
    const isFirst = index === 0;
    const isLast = index === editedSequence.length - 1;

    return (
      <List.Item
        key={index}
        actions={[
          <Button
            type="text"
            icon={<ArrowUpOutlined />}
            onClick={() => moveItem(index, 'up')}
            disabled={isFirst}
            title={t('macro.move_up')}
          />,
          <Button
            type="text"
            icon={<ArrowDownOutlined />}
            onClick={() => moveItem(index, 'down')}
            disabled={isLast}
            title={t('macro.move_down')}
          />,
          <Popconfirm
            title={t('macro.remove_item_confirm')}
            onConfirm={() => removeItem(index)}
          >
            <Button
              type="text"
              icon={<DeleteOutlined />}
              danger
              title={t('macro.remove_item')}
            />
          </Popconfirm>
        ]}
      >
        <div style={{ width: '100%' }}>
          {item.type === 'delay' ? (
            <Space>
              <Text>{t('macro.delay_ms')}:</Text>
              <InputNumber
                value={item.delay}
                onChange={(value) => updateItem(index, 'delay', value)}
                min={1}
                max={10000}
                style={{ width: 100 }}
              />
              <Text>ms</Text>
            </Space>
          ) : (
            <Space>
              <Text>{t('macro.key')}:</Text>
              <Select
                value={item.key}
                onChange={(value) => updateItem(index, 'key', value)}
                style={{ width: 120 }}
              >
                {getKeyOptions().map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
              <Text>{t('macro.action')}:</Text>
              <Select
                value={item.action}
                onChange={(value) => updateItem(index, 'action', value)}
                style={{ width: 100 }}
              >
                <Option value="tap">{t('macro.key_tap')}</Option>
                <Option value="down">{t('macro.key_down')}</Option>
                <Option value="up">{t('macro.key_up')}</Option>
              </Select>
            </Space>
          )}
        </div>
      </List.Item>
    );
  };

  const getKeyOptions = () => {
    return [
      // Letters
      ...Array.from({ length: 26 }, (_, i) => ({
        value: `Key${String.fromCharCode(65 + i)}`,
        label: String.fromCharCode(65 + i)
      })),
      // Numbers
      ...Array.from({ length: 10 }, (_, i) => ({
        value: `Digit${i}`,
        label: i.toString()
      })),
      // Special keys
      { value: 'Space', label: 'Space' },
      { value: 'Enter', label: 'Enter' },
      { value: 'Escape', label: 'Esc' },
      { value: 'Backspace', label: 'Backspace' },
      { value: 'Tab', label: 'Tab' },
      { value: 'ShiftLeft', label: 'L-Shift' },
      { value: 'ShiftRight', label: 'R-Shift' },
      { value: 'ControlLeft', label: 'L-Ctrl' },
      { value: 'ControlRight', label: 'R-Ctrl' },
      { value: 'AltLeft', label: 'L-Alt' },
      { value: 'AltRight', label: 'R-Alt' },
      // Function keys
      ...Array.from({ length: 12 }, (_, i) => ({
        value: `F${i + 1}`,
        label: `F${i + 1}`
      })),
      // Arrow keys
      { value: 'ArrowUp', label: '↑' },
      { value: 'ArrowDown', label: '↓' },
      { value: 'ArrowLeft', label: '←' },
      { value: 'ArrowRight', label: '→' }
    ];
  };

  return (
    <Modal
      title={`${t('macro.edit_macro')} - ${macroName}`}
      open={visible}
      onCancel={onCancel}
      width={900}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          {t('cancel')}
        </Button>,
        <Button key="save" type="primary" onClick={saveSequence}>
          <SaveOutlined /> {t('macro.save')}
        </Button>
      ]}
    >
      <div style={{ marginBottom: 16 }}>
        <Title level={4}>{t('macro.add_new_item')}</Title>
        <Space wrap>
          <Select
            value={newItemType}
            onChange={setNewItemType}
            style={{ width: 120 }}
          >
            <Option value="key">{t('macro.key')}</Option>
            <Option value="delay">{t('macro.delay')}</Option>
          </Select>
          
          {newItemType === 'key' ? (
            <>
              <Select
                value={newKeyCode}
                onChange={setNewKeyCode}
                style={{ width: 120 }}
                placeholder={t('macro.select_key')}
              >
                {getKeyOptions().map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
              <Select
                value={newKeyAction}
                onChange={setNewKeyAction}
                style={{ width: 100 }}
              >
                <Option value="tap">{t('macro.key_tap')}</Option>
                <Option value="down">{t('macro.key_down')}</Option>
                <Option value="up">{t('macro.key_up')}</Option>
              </Select>
            </>
          ) : (
            <>
              <InputNumber
                value={newDelay}
                onChange={setNewDelay}
                min={1}
                max={10000}
                style={{ width: 100 }}
              />
              <Text>ms</Text>
            </>
          )}
          
          <Button type="primary" icon={<PlusOutlined />} onClick={addItem}>
            {t('macro.add_item')}
          </Button>
        </Space>
      </div>

      <div>
        <Title level={4}>
          {t('macro.sequence')} ({editedSequence.length} {t('macro.items')})
        </Title>
        <div style={{ maxHeight: 400, overflow: 'auto' }}>
          {editedSequence.length > 0 ? (
            <List
              dataSource={editedSequence}
              renderItem={renderSequenceItem}
              size="small"
            />
          ) : (
            <Text type="secondary">{t('macro.empty_sequence')}</Text>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default MacroEditor;
