import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Modal, Button, Typography, Space, Alert, List, Tag } from 'antd';
import { PlayCircleOutlined, StopOutlined, SaveOutlined, ClearOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;

const MacroRecorder = ({ visible, onComplete, onCancel, macroName }) => {
  const { t } = useTranslation();
  const [isRecording, setIsRecording] = useState(false);
  const [recordedSequence, setRecordedSequence] = useState([]);
  const [startTime, setStartTime] = useState(null);
  const [lastEventTime, setLastEventTime] = useState(null);
  const recordingRef = useRef(false);
  const sequenceRef = useRef([]);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (visible) {
      setRecordedSequence([]);
      setIsRecording(false);
      setStartTime(null);
      setLastEventTime(null);
      sequenceRef.current = [];
      recordingRef.current = false;
    }
  }, [visible]);

  // Keyboard event handler
  const handleKeyEvent = useCallback((event) => {
    if (!recordingRef.current) return;

    const currentTime = Date.now();
    const keyCode = event.code;
    const isKeyDown = event.type === 'keydown';

    // Stop recording on ESC key
    if (keyCode === 'Escape') {
      stopRecording();
      return;
    }

    // Prevent default behavior during recording
    event.preventDefault();
    event.stopPropagation();

    // Calculate delay since last event
    let delay = 0;
    if (lastEventTime) {
      delay = currentTime - lastEventTime;
    }

    // Add delay if significant (> 10ms)
    if (delay > 10 && sequenceRef.current.length > 0) {
      sequenceRef.current.push({
        type: 'delay',
        delay: delay,
        timestamp: currentTime
      });
    }

    // Add key event
    sequenceRef.current.push({
      type: 'key',
      key: keyCode,
      action: isKeyDown ? 'down' : 'up',
      timestamp: currentTime
    });

    setLastEventTime(currentTime);
    setRecordedSequence([...sequenceRef.current]);
  }, []);

  // Set up event listeners
  useEffect(() => {
    if (isRecording) {
      document.addEventListener('keydown', handleKeyEvent, true);
      document.addEventListener('keyup', handleKeyEvent, true);
      
      return () => {
        document.removeEventListener('keydown', handleKeyEvent, true);
        document.removeEventListener('keyup', handleKeyEvent, true);
      };
    }
  }, [isRecording, handleKeyEvent]);

  const startRecording = () => {
    const now = Date.now();
    setIsRecording(true);
    setStartTime(now);
    setLastEventTime(now);
    setRecordedSequence([]);
    sequenceRef.current = [];
    recordingRef.current = true;
  };

  const stopRecording = () => {
    setIsRecording(false);
    recordingRef.current = false;
  };

  const clearSequence = () => {
    setRecordedSequence([]);
    sequenceRef.current = [];
  };

  const saveSequence = () => {
    // Convert recorded sequence to the format expected by the macro system
    const processedSequence = processRecordedSequence(recordedSequence);
    onComplete(processedSequence);
  };

  const processRecordedSequence = (sequence) => {
    // Convert the raw recorded sequence into a format suitable for macro playback
    // This includes optimizing key combinations and delays
    const processed = [];
    
    for (let i = 0; i < sequence.length; i++) {
      const item = sequence[i];
      
      if (item.type === 'delay') {
        // Only add delays that are meaningful (> 50ms)
        if (item.delay > 50) {
          processed.push({
            type: 'delay',
            delay: Math.min(item.delay, 5000) // Cap at 5 seconds
          });
        }
      } else if (item.type === 'key') {
        // Convert key codes to a standard format
        processed.push({
          type: 'key',
          key: item.key,
          action: item.action
        });
      }
    }
    
    return processed;
  };

  const renderSequenceItem = (item, index) => {
    if (item.type === 'delay') {
      return (
        <List.Item key={index}>
          <Tag color="orange">
            {t('macro.delay_ms').replace('{ms}', item.delay)}
          </Tag>
        </List.Item>
      );
    } else {
      return (
        <List.Item key={index}>
          <Tag color={item.action === 'down' ? 'green' : 'red'}>
            {item.key} {item.action === 'down' ? '↓' : '↑'}
          </Tag>
        </List.Item>
      );
    }
  };

  const getKeyDisplayName = (keyCode) => {
    // Convert key codes to display names
    const keyMap = {
      'KeyA': 'A', 'KeyB': 'B', 'KeyC': 'C', 'KeyD': 'D', 'KeyE': 'E',
      'KeyF': 'F', 'KeyG': 'G', 'KeyH': 'H', 'KeyI': 'I', 'KeyJ': 'J',
      'KeyK': 'K', 'KeyL': 'L', 'KeyM': 'M', 'KeyN': 'N', 'KeyO': 'O',
      'KeyP': 'P', 'KeyQ': 'Q', 'KeyR': 'R', 'KeyS': 'S', 'KeyT': 'T',
      'KeyU': 'U', 'KeyV': 'V', 'KeyW': 'W', 'KeyX': 'X', 'KeyY': 'Y',
      'KeyZ': 'Z',
      'Digit1': '1', 'Digit2': '2', 'Digit3': '3', 'Digit4': '4', 'Digit5': '5',
      'Digit6': '6', 'Digit7': '7', 'Digit8': '8', 'Digit9': '9', 'Digit0': '0',
      'Space': 'Space', 'Enter': 'Enter', 'Escape': 'Esc', 'Backspace': 'Backspace',
      'Tab': 'Tab', 'ShiftLeft': 'L-Shift', 'ShiftRight': 'R-Shift',
      'ControlLeft': 'L-Ctrl', 'ControlRight': 'R-Ctrl',
      'AltLeft': 'L-Alt', 'AltRight': 'R-Alt'
    };
    
    return keyMap[keyCode] || keyCode;
  };

  return (
    <Modal
      title={`${t('macro.record_new')} - ${macroName}`}
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={[
        <Button key="clear" onClick={clearSequence} disabled={isRecording}>
          <ClearOutlined /> {t('macro.clear')}
        </Button>,
        <Button key="cancel" onClick={onCancel} disabled={isRecording}>
          {t('cancel')}
        </Button>,
        <Button 
          key="save" 
          type="primary" 
          onClick={saveSequence}
          disabled={isRecording || recordedSequence.length === 0}
        >
          <SaveOutlined /> {t('macro.save')}
        </Button>
      ]}
    >
      <div style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Alert
            message={isRecording ? t('macro.recording') : t('macro.recording_tip')}
            type={isRecording ? "info" : "warning"}
            showIcon
          />
          
          <Space>
            <Button
              type={isRecording ? "danger" : "primary"}
              icon={isRecording ? <StopOutlined /> : <PlayCircleOutlined />}
              onClick={isRecording ? stopRecording : startRecording}
              size="large"
            >
              {isRecording ? t('macro.stop') : t('macro.record')}
            </Button>
            
            <Text>
              {t('macro.sequence')}: {recordedSequence.length} {t('macro.events')}
            </Text>
          </Space>
        </Space>
      </div>

      <div style={{ maxHeight: 400, overflow: 'auto' }}>
        <Title level={4}>{t('macro.recorded_sequence')}</Title>
        {recordedSequence.length > 0 ? (
          <List
            dataSource={recordedSequence}
            renderItem={renderSequenceItem}
            size="small"
          />
        ) : (
          <Text type="secondary">{t('macro.no_sequence')}</Text>
        )}
      </div>
    </Modal>
  );
};

export default MacroRecorder;
