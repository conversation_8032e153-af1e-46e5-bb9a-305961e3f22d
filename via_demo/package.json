{"license": "GPL-3.0", "scripts": {"dev": "node scripts/build-definitions.js && vite --force", "build": "bun run build:kbs && tsc && vite build", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "lint": "prettier --check \"src/**/*.{js,jsx,ts,tsx}\"", "refresh-kbs": "bun uninstall --force via-keyboards && bun install --force github:the-via/keyboards", "build:azure": "bun run refresh-kbs && bun run build", "preview": "vite preview", "build:kbs": "via-keyboards public/definitions", "find-deadcode": "ts-prune"}, "engines": {"node": ">=18.0.0"}, "overrides": {"typescript": "^5.6.2"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.3.0", "@fortawesome/fontawesome-svg-core": "^6.3.0", "@fortawesome/free-brands-svg-icons": "^6.3.0", "@fortawesome/free-solid-svg-icons": "^6.3.0", "@fortawesome/react-fontawesome": "^0.2.0", "@microsoft/applicationinsights-web": "^2.8.10", "@react-hook/resize-observer": "^1.2.6", "@react-spring/three": "^9.7.5", "@react-three/drei": "^9.114.2", "@react-three/fiber": "^8.17.9", "@reduxjs/toolkit": "^2.3.0", "@the-via/pelpi": "^0.0.3", "@the-via/reader": "^1.9.0", "@webscopeio/react-textarea-autocomplete": "^4.9.2", "downshift": "^7.2.1", "idb-keyval": "^6.2.0", "json-stringify-pretty-compact": "^4.0.0", "lodash.defaultsdeep": "^4.6.1", "lodash.partition": "^4.6.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-is": "^18.3.1", "react-redux": "^9.1.2", "react-select": "^5.8.1", "react-textarea-autosize": "^8.4.0", "redux-logger": "^3.0.6", "styled-components": "^6.0.0-beta.5", "three": "^0.169.0", "use-resize-observer": "^9.1.0", "via-keyboards": "github:the-via/keyboards", "wouter": "^2.10.0"}, "devDependencies": {"@types/lodash.defaultsdeep": "^4.6.7", "@types/lodash.partition": "^4.6.7", "@types/raf-schd": "^4.0.1", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/three": "^0.169.0", "@types/w3c-web-hid": "^1.0.3", "@types/webscopeio__react-textarea-autocomplete": "^4.7.2", "@types/wicg-file-system-access": "^2020.9.5", "@vitejs/plugin-react": "^3.1.0", "concurrently": "^7.6.0", "husky": "^8.0.3", "typescript": "^5.6.2", "vite": "^4.1.4", "vite-plugin-html": "^3.2.0"}}