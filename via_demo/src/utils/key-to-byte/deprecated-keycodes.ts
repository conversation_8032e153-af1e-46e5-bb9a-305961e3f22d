export default {
  KC_TILD: 'S(KC_GRV)',
  KC_EXLM: 'S(KC_1)',
  KC_AT: 'S(KC_2)',
  KC_HASH: 'S(KC_3)',
  KC_DLR: 'S(KC_4)',
  KC_PERC: 'S(KC_5)',
  KC_CIRC: 'S(KC_6)',
  KC_AMPR: 'S(KC_7)',
  KC_ASTR: 'S(KC_8)',
  KC_LPRN: 'S(KC_9)',
  KC_RPRN: 'S(KC_0)',
  KC_UNDS: 'S(KC_MINS)',
  KC_PLUS: 'S(KC_EQL)',
  KC_LCBR: 'S(KC_LBRC)',
  KC_RCBR: 'S(KC_RBRC)',
  KC_PIPE: 'S(KC_BSLS)',
  KC_COLN: 'S(KC_SCLN)',
  <PERSON>_<PERSON>QU<PERSON>: 'S(KC_QUOT)',
  KC_LT: 'S(KC_COMM)',
  KC_GT: 'S(KC_DOT)',
  KC_QUES: 'S(KC_SLSH)',
  SPC_FN1: 'LT(1,KC_SPC)',
  SPC_FN2: 'LT(2,KC_SPC)',
  SPC_FN3: 'LT(3,KC_SPC)',
  MACRO00: 'MACRO(0)',
  MACRO01: 'MACRO(1)',
  MACRO02: 'MACRO(2)',
  MACRO03: 'MACRO(3)',
  MACRO04: 'MACRO(4)',
  MACRO05: 'MACRO(5)',
  MACRO06: 'MACRO(6)',
  MACRO07: 'MACRO(7)',
  MACRO08: 'MACRO(8)',
  MACRO09: 'MACRO(9)',
  MACRO10: 'MACRO(10)',
  MACRO11: 'MACRO(11)',
  MACRO12: 'MACRO(12)',
  MACRO13: 'MACRO(13)',
  MACRO14: 'MACRO(14)',
  MACRO15: 'MACRO(15)',
  USER00: 'CUSTOM(0)',
  USER01: 'CUSTOM(1)',
  USER02: 'CUSTOM(2)',
  USER03: 'CUSTOM(3)',
  USER04: 'CUSTOM(4)',
  USER05: 'CUSTOM(5)',
  USER06: 'CUSTOM(6)',
  USER07: 'CUSTOM(7)',
  USER08: 'CUSTOM(8)',
  USER09: 'CUSTOM(9)',
  USER10: 'CUSTOM(10)',
  USER11: 'CUSTOM(11)',
  USER12: 'CUSTOM(12)',
  USER13: 'CUSTOM(13)',
  USER14: 'CUSTOM(14)',
  USER15: 'CUSTOM(15)',
} as Record<string, string>;
